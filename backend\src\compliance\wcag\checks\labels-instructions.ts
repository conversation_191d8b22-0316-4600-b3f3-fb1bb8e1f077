/**
 * WCAG-030: Labels or Instructions Check
 * Success Criterion: 3.3.2 Labels or Instructions (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { AdvancedPatternDetector } from '../utils/advanced-pattern-detector';
import { PatternRecognitionEngine } from '../utils/pattern-recognition-engine';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface FormControlAnalysis {
  element: string;
  type: string;
  selector: string;
  hasLabel: boolean;
  hasAriaLabel: boolean;
  hasAriaLabelledby: boolean;
  hasAriaDescribedby: boolean;
  hasPlaceholder: boolean;
  hasTitle: boolean;
  hasInstructions: boolean;
  isRequired: boolean;
  labelText?: string;
  placeholderText?: string;
  instructionText?: string;
  isAccessible: boolean;
  issues: string[];
}

interface LabelsInstructionsAnalysis {
  formControls: FormControlAnalysis[];
  totalControls: number;
  controlsWithLabels: number;
  controlsWithInstructions: number;
  controlsWithIssues: number;
  requiredControls: number;
  requiredControlsWithInstructions: number;
}

export interface LabelsInstructionsConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class LabelsInstructionsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  async performCheck(config: LabelsInstructionsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LabelsInstructionsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-030',
      'Labels or Instructions',
      'understandable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeLabelsInstructionsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with form label analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-030',
        ruleName: 'Labels or Instructions',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'form-label-analysis',
          labelAssociationValidation: true,
          instructionDetection: true,
          ariaLabelValidation: true,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 40,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'labels-instructions-analysis',
        confidence: 0.80,
        additionalData: {
          checkType: 'form-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeLabelsInstructionsCheck(
    page: Page,
    _config: LabelsInstructionsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeGrouping: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze labels and instructions using enhanced analyzer
    const labelsAnalysis = await this.analyzeLabelsAndInstructionsEnhanced(
      page,
      formAccessibilityReport,
    );

    // Analyze instruction quality using AI semantic validation
    const instructionQualityAnalysis = await this.analyzeInstructionQuality(page);

    // Combine analysis results
    const allAnalyses = [labelsAnalysis, instructionQualityAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced labels and instructions analysis using FormAccessibilityAnalyzer
   */
  private async analyzeLabelsAndInstructionsEnhanced(
    page: Page,
    formAccessibilityReport: any,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form: any, formIndex: number) => {
      form.fields.forEach((field: any, fieldIndex: number) => {
        totalChecks++;

        // Check if field has proper labeling
        if (field.hasLabel && field.isAccessible) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Form field ${fieldIndex + 1} in form ${formIndex + 1} has proper labeling`,
            value: `${field.selector} - label: "${field.labelText}", accessible: ${field.isAccessible}`,
            selector: field.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Form field ${fieldIndex + 1} in form ${formIndex + 1} lacks proper labeling`);
          evidence.push({
            type: 'code',
            description: `Form field ${fieldIndex + 1} requires proper labeling`,
            value: `${field.selector} - hasLabel: ${field.hasLabel}, isAccessible: ${field.isAccessible}`,
            selector: field.selector,
            severity: 'error',
          });
          recommendations.push(`Add proper label to form field ${fieldIndex + 1} in form ${formIndex + 1}`);
        }

        // Check for required field instructions
        if (field.isRequired && !field.hasInstructions) {
          issues.push(`Required field ${fieldIndex + 1} lacks instructions`);
          evidence.push({
            type: 'code',
            description: `Required field ${fieldIndex + 1} needs instructions`,
            value: `${field.selector} - required field without instructions`,
            selector: field.selector,
            severity: 'warning',
          });
          recommendations.push(`Add instructions for required field ${fieldIndex + 1}`);
        }

        // Check for validation instructions
        if (field.hasValidation && !field.hasValidationInstructions) {
          issues.push(`Field ${fieldIndex + 1} with validation lacks format instructions`);
          evidence.push({
            type: 'code',
            description: `Field ${fieldIndex + 1} needs validation instructions`,
            value: `${field.selector} - has validation but no format instructions`,
            selector: field.selector,
            severity: 'warning',
          });
          recommendations.push(`Add format instructions for field ${fieldIndex + 1}`);
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze instruction quality using AI semantic validation
   */
  private async analyzeInstructionQuality(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Find instruction elements
    const instructions = await page.$$eval(
      'label, .help-text, .instruction, [aria-describedby], .form-help, .field-description',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.trim() || '';
          const isVisible = element.offsetParent !== null;
          const associatedField = element.getAttribute('for') ||
                                 element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          return {
            index,
            text,
            isVisible,
            hasAssociatedField: !!associatedField,
            selector: `instruction-${index}`,
            isEmpty: text.length === 0,
            isVague: text.length < 10 || text.toLowerCase().includes('enter') && text.length < 20,
          };
        });
      },
    );

    const totalChecks = instructions.length;
    let passedChecks = 0;

    instructions.forEach((instruction, index) => {
      let instructionPassed = true;

      // Check if instruction is visible and has content
      if (!instruction.isVisible || instruction.isEmpty) {
        instructionPassed = false;
        issues.push(`Instruction ${index + 1} is not visible or empty`);
        evidence.push({
          type: 'code',
          description: `Instruction ${index + 1} visibility issue`,
          value: `visible: ${instruction.isVisible}, hasContent: ${!instruction.isEmpty}`,
          severity: 'error',
        });
        recommendations.push(`Ensure instruction ${index + 1} is visible and has meaningful content`);
      }

      // Check for vague instructions
      if (instruction.isVague && instruction.text.length > 0) {
        issues.push(`Instruction ${index + 1} is too vague`);
        evidence.push({
          type: 'code',
          description: `Instruction ${index + 1} needs more specific content`,
          value: `instruction: "${instruction.text}"`,
          severity: 'warning',
        });
        recommendations.push(`Make instruction ${index + 1} more specific and helpful`);
      }

      if (instructionPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Instruction ${index + 1} is properly implemented`,
          value: `"${instruction.text}" - visible and meaningful`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

}

      formControls.forEach((control, index) => {
        const tagName = control.tagName.toLowerCase();
        const type = control.getAttribute('type') || tagName;
        const id = control.getAttribute('id');
        const name = control.getAttribute('name');
        
        // Check for various labeling methods
        const hasLabel = !!document.querySelector(`label[for="${id}"]`) || 
                        !!control.closest('label');
        const hasAriaLabel = !!control.getAttribute('aria-label');
        const hasAriaLabelledby = !!control.getAttribute('aria-labelledby');
        const hasAriaDescribedby = !!control.getAttribute('aria-describedby');
        const hasPlaceholder = !!control.getAttribute('placeholder');
        const hasTitle = !!control.getAttribute('title');
        
        // Get label text
        let labelText = '';
        if (hasLabel) {
          const labelElement = document.querySelector(`label[for="${id}"]`) || 
                              control.closest('label');
          labelText = labelElement?.textContent?.trim() || '';
        } else if (hasAriaLabel) {
          labelText = control.getAttribute('aria-label') || '';
        } else if (hasAriaLabelledby) {
          const labelledbyId = control.getAttribute('aria-labelledby');
          const labelledbyElement = labelledbyId ? document.getElementById(labelledbyId) : null;
          labelText = labelledbyElement?.textContent?.trim() || '';
        }

        // Check for instructions
        let instructionText = '';
        let hasInstructions = false;
        
        if (hasAriaDescribedby) {
          const describedbyId = control.getAttribute('aria-describedby');
          const describedbyElement = describedbyId ? document.getElementById(describedbyId) : null;
          instructionText = describedbyElement?.textContent?.trim() || '';
          hasInstructions = !!instructionText;
        }

        // Look for nearby instruction text
        if (!hasInstructions) {
          const parent = control.parentElement;
          const siblings = parent ? Array.from(parent.children) : [];
          const instructionElements = siblings.filter(el => 
            el.classList.contains('help-text') ||
            el.classList.contains('instruction') ||
            el.classList.contains('hint') ||
            el.tagName.toLowerCase() === 'small' ||
            el.getAttribute('role') === 'note'
          );
          
          if (instructionElements.length > 0) {
            instructionText = instructionElements[0].textContent?.trim() || '';
            hasInstructions = !!instructionText;
          }
        }

        const isRequired = control.hasAttribute('required') || 
                          control.getAttribute('aria-required') === 'true';

        // Determine accessibility and issues
        const issues: string[] = [];
        let isAccessible = true;

        if (!hasLabel && !hasAriaLabel && !hasAriaLabelledby) {
          issues.push('Missing accessible label');
          isAccessible = false;
        }

        if (isRequired && !hasInstructions && !labelText.toLowerCase().includes('required')) {
          issues.push('Required field without clear indication');
        }

        if (type === 'password' && !hasInstructions) {
          issues.push('Password field without format instructions');
        }

        if ((type === 'email' || type === 'tel' || type === 'url') && !hasInstructions) {
          issues.push('Specialized input without format instructions');
        }

        controlAnalysis.push({
          element: tagName,
          type,
          selector: `${tagName}:nth-of-type(${index + 1})`,
          hasLabel,
          hasAriaLabel,
          hasAriaLabelledby,
          hasAriaDescribedby,
          hasPlaceholder,
          hasTitle,
          hasInstructions,
          isRequired,
          labelText,
          placeholderText: control.getAttribute('placeholder') || undefined,
          instructionText: instructionText || undefined,
          isAccessible,
          issues,
        });
      });

      const totalControls = controlAnalysis.length;
      const controlsWithLabels = controlAnalysis.filter(c => c.hasLabel || c.hasAriaLabel || c.hasAriaLabelledby).length;
      const controlsWithInstructions = controlAnalysis.filter(c => c.hasInstructions).length;
      const controlsWithIssues = controlAnalysis.filter(c => c.issues.length > 0).length;
      const requiredControls = controlAnalysis.filter(c => c.isRequired).length;
      const requiredControlsWithInstructions = controlAnalysis.filter(c => 
        c.isRequired && (c.hasInstructions || c.labelText?.toLowerCase().includes('required'))
      ).length;

      return {
        formControls: controlAnalysis,
        totalControls,
        controlsWithLabels,
        controlsWithInstructions,
        controlsWithIssues,
        requiredControls,
        requiredControlsWithInstructions,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = labelsAnalysis.totalControls;

    if (totalElements === 0) {
      // No form controls found
      evidence.push({
        type: 'info',
        description: 'No form controls found on page',
        value: 'Page contains no form controls requiring labels or instructions',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalControls: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future form controls include proper labels and instructions'],
      };
    }

    // Analyze controls with issues
    const controlsWithIssues = labelsAnalysis.formControls.filter(control => control.issues.length > 0);
    
    if (controlsWithIssues.length > 0) {
      const failureRate = controlsWithIssues.length / totalElements;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      controlsWithIssues.forEach((control) => {
        issues.push(`${control.element}[${control.type}]: ${control.issues.join(', ')}`);
        
        evidence.push({
          type: 'error',
          description: `Form control missing labels or instructions`,
          value: `${control.element}[type="${control.type}"] - ${control.issues.join(', ')}`,
          selector: control.selector,
          elementCount: 1,
          affectedSelectors: [control.selector],
          severity: 'error',
          fixExample: {
            before: this.generateBeforeExample(control),
            after: this.generateAfterExample(control),
            description: 'Add proper labels and instructions for form controls',
            codeExample: `
<!-- Method 1: Using label element -->
<label for="email">Email Address (required)</label>
<input type="email" id="email" required aria-describedby="email-help">
<div id="email-help">Enter a valid email <NAME_EMAIL></div>

<!-- Method 2: Using aria-label -->
<input type="password" aria-label="Password" aria-describedby="pwd-help">
<div id="pwd-help">Password must be at least 8 characters long</div>

<!-- Method 3: Using aria-labelledby -->
<div id="phone-label">Phone Number</div>
<input type="tel" aria-labelledby="phone-label" aria-describedby="phone-help">
<div id="phone-help">Format: (*************</div>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/labels-or-instructions.html',
              'https://webaim.org/techniques/forms/controls',
              'https://www.w3.org/WAI/tutorials/forms/labels/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              controlType: control.type,
              hasLabel: control.hasLabel,
              hasAriaLabel: control.hasAriaLabel,
              hasInstructions: control.hasInstructions,
              isRequired: control.isRequired,
              issues: control.issues,
            },
          },
        });
      });
    }

    // Add summary evidence
    evidence.push({
      type: score === 100 ? 'info' : 'warning',
      description: 'Form controls labels and instructions analysis',
      value: `${totalElements} form controls: ${labelsAnalysis.controlsWithLabels} with labels, ${controlsWithIssues.length} with issues`,
      selector: 'form, body',
      elementCount: totalElements,
      affectedSelectors: ['input', 'select', 'textarea'],
      severity: score === 100 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalControls: labelsAnalysis.totalControls,
          controlsWithLabels: labelsAnalysis.controlsWithLabels,
          controlsWithInstructions: labelsAnalysis.controlsWithInstructions,
          controlsWithIssues: labelsAnalysis.controlsWithIssues,
          requiredControls: labelsAnalysis.requiredControls,
          requiredControlsWithInstructions: labelsAnalysis.requiredControlsWithInstructions,
          labelingRate: totalElements > 0 ? (labelsAnalysis.controlsWithLabels / totalElements * 100).toFixed(1) : '0',
        },
      },
    });

    // Generate recommendations
    if (controlsWithIssues.length > 0) {
      recommendations.push('Provide accessible labels for all form controls');
      recommendations.push('Use label elements, aria-label, or aria-labelledby for labeling');
      recommendations.push('Include clear instructions for required fields and input formats');
      recommendations.push('Use aria-describedby to associate instructions with form controls');
    } else {
      recommendations.push('Continue using proper labels and instructions for form controls');
      recommendations.push('Test forms with screen readers to verify accessibility');
    }

    if (labelsAnalysis.requiredControls > labelsAnalysis.requiredControlsWithInstructions) {
      recommendations.push('Clearly indicate required fields in labels or instructions');
    }

    const specializedControls = labelsAnalysis.formControls.filter(c => 
      ['email', 'tel', 'url', 'password'].includes(c.type)
    );
    if (specializedControls.length > 0) {
      recommendations.push('Provide format instructions for specialized input types');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateBeforeExample(control: FormControlAnalysis): string {
    if (control.type === 'password') {
      return '<input type="password">';
    } else if (control.type === 'email') {
      return '<input type="email">';
    } else if (control.element === 'select') {
      return '<select><option>Choose option</option></select>';
    } else {
      return `<input type="${control.type}">`;
    }
  }

  private generateAfterExample(control: FormControlAnalysis): string {
    if (control.type === 'password') {
      return `<label for="pwd">Password (required)</label>
<input type="password" id="pwd" required aria-describedby="pwd-help">
<div id="pwd-help">Must be at least 8 characters</div>`;
    } else if (control.type === 'email') {
      return `<label for="email">Email Address</label>
<input type="email" id="email" aria-describedby="email-help">
<div id="email-help">Enter a valid <NAME_EMAIL></div>`;
    } else if (control.element === 'select') {
      return `<label for="country">Country</label>
<select id="country">
  <option value="">Choose a country</option>
  <option value="us">United States</option>
</select>`;
    } else {
      return `<label for="field">${control.type.charAt(0).toUpperCase() + control.type.slice(1)}</label>
<input type="${control.type}" id="field">`;
    }
  }
}
