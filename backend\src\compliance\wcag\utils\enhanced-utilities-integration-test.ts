/**
 * Enhanced Utilities Integration Test
 * Validates all enhanced utilities work together correctly and maintain backward compatibility
 */

import EnhancedUtilitiesCoordinator from './enhanced-utilities-coordinator';
import logger from '../../../utils/logger';

export interface IntegrationTestResult {
  testName: string;
  passed: boolean;
  duration: number;
  details: string;
  error?: string;
}

export interface IntegrationTestSuite {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
  results: IntegrationTestResult[];
  overallPassed: boolean;
}

/**
 * Comprehensive integration test for enhanced WCAG utilities
 */
export class EnhancedUtilitiesIntegrationTest {
  private coordinator: EnhancedUtilitiesCoordinator;
  private testResults: IntegrationTestResult[] = [];

  constructor() {
    this.coordinator = EnhancedUtilitiesCoordinator.getInstance({
      enableIntegratedOptimization: true,
      enableCrossUtilityAnalytics: true,
      cache: {
        enableCompression: true,
        enableCacheWarming: true,
        enableAnalytics: true,
      },
      browserPool: {
        enableLoadBalancing: true,
        enableHealthMonitoring: true,
        enablePredictiveSpawning: true,
        enableAutoRecovery: true,
      },
      performanceMonitor: {
        enableTrendAnalysis: true,
        enableAutoOptimization: true,
        enableResourceCorrelation: true,
        enableRealTimeAlerts: true,
      },
    });
  }

  /**
   * Run complete integration test suite
   */
  async runIntegrationTests(): Promise<IntegrationTestSuite> {
    logger.info('🧪 Starting Enhanced Utilities Integration Tests');
    const startTime = Date.now();

    // Initialize coordinator
    await this.coordinator.initialize();

    // Run all test categories
    await this.testCacheIntegration();
    await this.testBrowserPoolIntegration();
    await this.testPerformanceMonitorIntegration();
    await this.testCrossUtilityIntegration();
    await this.testBackwardCompatibility();
    await this.testPerformanceImprovements();

    const totalDuration = Date.now() - startTime;
    const passedTests = this.testResults.filter((r) => r.passed).length;
    const failedTests = this.testResults.filter((r) => !r.passed).length;

    const suite: IntegrationTestSuite = {
      suiteName: 'Enhanced WCAG Utilities Integration',
      totalTests: this.testResults.length,
      passedTests,
      failedTests,
      totalDuration,
      results: this.testResults,
      overallPassed: failedTests === 0,
    };

    // Cleanup
    await this.coordinator.shutdown();

    logger.info(
      `🧪 Integration Tests Completed: ${passedTests}/${this.testResults.length} passed in ${totalDuration}ms`,
    );

    return suite;
  }

  /**
   * Test enhanced cache functionality
   */
  private async testCacheIntegration(): Promise<void> {
    await this.runTest('Enhanced Cache - Compression', async () => {
      const cache = this.coordinator.getCache();
      const testData = {
        large: 'x'.repeat(10000),
        complex: { nested: { data: Array(1000).fill('test') } },
      };

      await cache.setCompressed('test-compression', testData, 'rule');
      const retrieved = await cache.getCompressed('test-compression', 'rule');

      if (!retrieved || JSON.stringify(retrieved) !== JSON.stringify(testData)) {
        throw new Error('Compression/decompression failed');
      }

      return 'Compression and decompression working correctly';
    });

    await this.runTest('Enhanced Cache - Analytics', async () => {
      const cache = this.coordinator.getCache();
      const stats = cache.getEnhancedStats();

      if (!stats.analytics || typeof stats.analytics.compressionRatio !== 'number') {
        throw new Error('Analytics not available');
      }

      return `Analytics available: compression ratio ${stats.analytics.compressionRatio.toFixed(2)}`;
    });

    await this.runTest('Enhanced Cache - Warming', async () => {
      const cache = this.coordinator.getCache();

      await cache.warmCache([
        { type: 'rule', pattern: 'test-rule', priority: 10, frequency: 5 },
        { type: 'pattern', pattern: 'test-pattern', priority: 8, frequency: 3 },
      ]);

      return 'Cache warming completed successfully';
    });
  }

  /**
   * Test enhanced browser pool functionality
   */
  private async testBrowserPoolIntegration(): Promise<void> {
    await this.runTest('Enhanced Browser Pool - Load Balancing', async () => {
      const browserPool = this.coordinator.getBrowserPool();

      // Test optimal page allocation
      const page1 = await browserPool.getOptimalPage(
        { type: 'light', estimatedDuration: 10000, resourceIntensive: false, priority: 1 },
        'test-scan-1',
      );

      const page2 = await browserPool.getOptimalPage(
        { type: 'heavy', estimatedDuration: 60000, resourceIntensive: true, priority: 2 },
        'test-scan-2',
      );

      if (!page1 || !page2) {
        throw new Error('Failed to allocate optimal pages');
      }

      return 'Load balancing allocation working correctly';
    });

    await this.runTest('Enhanced Browser Pool - Health Monitoring', async () => {
      const browserPool = this.coordinator.getBrowserPool();
      const healthReports = await browserPool.monitorBrowserHealth();

      if (!Array.isArray(healthReports)) {
        throw new Error('Health monitoring not returning reports');
      }

      return `Health monitoring active: ${healthReports.length} browsers monitored`;
    });

    await this.runTest('Enhanced Browser Pool - Statistics', async () => {
      const browserPool = this.coordinator.getBrowserPool();
      const stats = browserPool.getEnhancedStats();

      if (!stats.loadBalancing || !stats.healthMetrics) {
        throw new Error('Enhanced statistics not available');
      }

      return `Enhanced stats available: ${stats.healthMetrics.length} browsers, efficiency ${stats.loadBalancing.balancingEfficiency.toFixed(1)}%`;
    });
  }

  /**
   * Test enhanced performance monitor functionality
   */
  private async testPerformanceMonitorIntegration(): Promise<void> {
    await this.runTest('Enhanced Performance Monitor - Trend Analysis', async () => {
      const perfMonitor = this.coordinator.getPerformanceMonitor();
      const trends = await perfMonitor.analyzeTrends(1); // 1 hour window

      if (!trends || !trends.scanDurationTrend) {
        throw new Error('Trend analysis not working');
      }

      return `Trend analysis working: overall trend ${trends.overallTrend}`;
    });

    await this.runTest('Enhanced Performance Monitor - Resource Correlation', async () => {
      const perfMonitor = this.coordinator.getPerformanceMonitor();
      const correlations = await perfMonitor.analyzeResourceCorrelation();

      if (!correlations || typeof correlations.memoryVsPerformance !== 'number') {
        throw new Error('Resource correlation analysis not working');
      }

      return `Resource correlation analysis working: ${correlations.insights.length} insights generated`;
    });

    await this.runTest('Enhanced Performance Monitor - Optimization Recommendations', async () => {
      const perfMonitor = this.coordinator.getPerformanceMonitor();
      const recommendations = await perfMonitor.optimizeParameters();

      if (!recommendations || !Array.isArray(recommendations.immediate)) {
        throw new Error('Optimization recommendations not working');
      }

      return `Optimization recommendations generated: ${recommendations.immediate.length} immediate, ${recommendations.automatedActions.length} automated`;
    });
  }

  /**
   * Test cross-utility integration
   */
  private async testCrossUtilityIntegration(): Promise<void> {
    await this.runTest('Cross-Utility - Health Report', async () => {
      const healthReport = await this.coordinator.getHealthReport();

      if (
        !healthReport.overall ||
        !healthReport.cache ||
        !healthReport.browserPool ||
        !healthReport.performanceMonitor
      ) {
        throw new Error('Health report missing components');
      }

      return `Health report complete: overall status ${healthReport.overall.status}, score ${healthReport.overall.performanceScore}`;
    });

    await this.runTest('Cross-Utility - Integrated Optimization', async () => {
      const optimization = await this.coordinator.performIntegratedOptimization();

      if (
        !optimization.estimatedImpact ||
        typeof optimization.estimatedImpact.performanceGain !== 'number'
      ) {
        throw new Error('Integrated optimization not working');
      }

      return `Integrated optimization complete: ${optimization.estimatedImpact.performanceGain}% performance gain estimated`;
    });

    await this.runTest('Cross-Utility - Coordinated Operations', async () => {
      // Test coordinated cache and page operations
      await this.coordinator.cacheWithMonitoring('test-key', { test: 'data' }, 'rule');
      const cached = await this.coordinator.getCacheWithMonitoring('test-key', 'rule');
      const page = await this.coordinator.getOptimalPage('test-scan');

      if (!cached || !page) {
        throw new Error('Coordinated operations failed');
      }

      return 'Coordinated cache and browser operations working correctly';
    });
  }

  /**
   * Test backward compatibility
   */
  private async testBackwardCompatibility(): Promise<void> {
    await this.runTest('Backward Compatibility - Cache Methods', async () => {
      await this.coordinator.cacheData('compat-test', { legacy: true }, 'rule');
      const data = await this.coordinator.getCachedData('compat-test', 'rule');

      if (!data || !data.legacy) {
        throw new Error('Legacy cache methods not working');
      }

      return 'Legacy cache methods working correctly';
    });

    await this.runTest('Backward Compatibility - Performance Monitoring', async () => {
      this.coordinator.startScanMonitoring('compat-scan');
      this.coordinator.recordCheckStart('compat-scan', 'WCAG-001', 'Test Check');
      this.coordinator.recordCheckEnd('compat-scan', 'WCAG-001', true);
      const report = this.coordinator.completeScanMonitoring('compat-scan');

      if (!report || !report.metrics) {
        throw new Error('Legacy performance monitoring not working');
      }

      return 'Legacy performance monitoring methods working correctly';
    });

    await this.runTest('Backward Compatibility - Page Allocation', async () => {
      const page = await this.coordinator.getPage('compat-page-test');

      if (!page) {
        throw new Error('Legacy page allocation not working');
      }

      return 'Legacy page allocation working correctly';
    });
  }

  /**
   * Test performance improvements
   */
  private async testPerformanceImprovements(): Promise<void> {
    await this.runTest('Performance - Cache Efficiency', async () => {
      const cache = this.coordinator.getCache();

      // Perform multiple cache operations to test efficiency
      const startTime = Date.now();
      for (let i = 0; i < 100; i++) {
        await cache.setCompressed(`perf-test-${i}`, { data: `test-${i}` }, 'rule');
      }
      const cacheTime = Date.now() - startTime;

      const retrieveStart = Date.now();
      for (let i = 0; i < 100; i++) {
        await cache.getCompressed(`perf-test-${i}`, 'rule');
      }
      const retrieveTime = Date.now() - retrieveStart;

      return `Cache performance: ${cacheTime}ms for 100 sets, ${retrieveTime}ms for 100 gets`;
    });

    await this.runTest('Performance - Memory Usage', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform memory-intensive operations
      const cache = this.coordinator.getCache();
      const largeData = Array(1000)
        .fill(0)
        .map((_, i) => ({ id: i, data: 'x'.repeat(1000) }));

      await cache.setCompressed('memory-test', largeData, 'rule');
      await cache.getCompressed('memory-test', 'rule');

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB

      return `Memory usage increase: ${memoryIncrease.toFixed(2)}MB for large data operations`;
    });
  }

  /**
   * Helper method to run individual tests
   */
  private async runTest(testName: string, testFunction: () => Promise<string>): Promise<void> {
    const startTime = Date.now();

    try {
      const details = await testFunction();
      const duration = Date.now() - startTime;

      this.testResults.push({
        testName,
        passed: true,
        duration,
        details,
      });

      logger.debug(`✅ ${testName}: ${details} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.testResults.push({
        testName,
        passed: false,
        duration,
        details: 'Test failed',
        error: errorMessage,
      });

      logger.error(`❌ ${testName}: ${errorMessage} (${duration}ms)`);
    }
  }
}

export default EnhancedUtilitiesIntegrationTest;
