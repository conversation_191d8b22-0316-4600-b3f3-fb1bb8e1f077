/**
 * WCAG-024: Language of Page Check
 * Success Criterion: 3.1.1 Language of Page (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { HeadlessCMSDetector } from '../utils/headless-cms-detector';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface LanguageDetectionResult {
  detectedLanguage: string;
  confidence: number;
  method: 'attribute' | 'content-analysis' | 'meta-tag' | 'heuristic';
  supportingEvidence: string[];
  inconsistencies: string[];
}

interface LangAttributeValidation {
  hasLangAttribute: boolean;
  langValue: string;
  isValidLanguageCode: boolean;
  isISO639Compliant: boolean;
  hasRegionCode: boolean;
  attributeLocation: 'html' | 'body' | 'missing';
}

interface MultilingualContentAnalysis {
  primaryLanguage: string;
  secondaryLanguages: string[];
  languageSwitches: number;
  hasLanguageNavigation: boolean;
  contentLanguageConsistency: number;
  mixedLanguageElements: string[];
}

export interface HtmlLangConfig extends EnhancedCheckConfig {
  enableAdvancedLanguageDetection?: boolean;
  enableLangAttributeValidation?: boolean;
  enableMultilingualContentAnalysis?: boolean;
  enableLanguageConsistencyTesting?: boolean;
  enableLanguageDetection?: boolean;
  enableSemanticValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableCMSDetection?: boolean;
  checkLanguageConsistency?: boolean;
  enableAISemanticValidation?: boolean;
}

export class HtmlLangCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private headlessCMSDetector = HeadlessCMSDetector.getInstance();

  async performCheck(config: HtmlLangConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized language detection
    const enhancedConfig: HtmlLangConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 1000, // Target: <1s performance
      },
      enableAdvancedLanguageDetection: true,
      enableLangAttributeValidation: true,
      enableMultilingualContentAnalysis: true,
      enableLanguageConsistencyTesting: true,
      enableLanguageDetection: true,
      enableSemanticValidation: true,
      enableContentQualityAnalysis: true,
      enableCMSDetection: true,
      checkLanguageConsistency: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-024',
      'Language of Page',
      'understandable',
      0.0611,
      'A',
      enhancedConfig,
      this.executeHtmlLangCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with HTML language analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-024',
        ruleName: 'Language of Page',
        scanDuration: result.executionTime,
        elementsAnalyzed: 1,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'html-language-validation',
          langAttributeValidation: true,
          pageLanguageDetection: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.95,
        maxEvidenceItems: 5,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: 1, // Always 1 html element
        failed: result.score === 0 ? 1 : 0,
        passed: result.score === 100 ? 1 : 0,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: 1,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'html-lang-detection',
        confidence: 1.0,
        additionalData: {
          checkType: 'language-validation',
          automationLevel: 'full',
        },
      },
    };
  }

  private async executeHtmlLangCheck(
    page: Page,
    _config: HtmlLangConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Language Detection Algorithm - Advanced Implementation
    const advancedLanguageDetection = await this.executeAdvancedLanguageDetection(page);

    // Lang Attribute Validation Algorithm
    const langAttributeValidation = await this.validateLangAttribute(page);

    // Multilingual Content Analysis Algorithm
    const multilingualAnalysis = await this.analyzeMultilingualContent(page);

    // Language Consistency Testing Algorithm
    const consistencyAnalysis = await this.testLanguageConsistency(page);

    // Combine all specialized detection results
    const allAnalyses = [
      advancedLanguageDetection,
      langAttributeValidation,
      multilingualAnalysis,
      consistencyAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 95% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Advanced Language Detection Algorithm - Core Implementation
   * Target: 95% language detection validation accuracy
   */
  private async executeAdvancedLanguageDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const languageDetection = await page.evaluate((): LanguageDetectionResult => {
      // Advanced language detection using multiple methods
      const html = document.documentElement;
      const langAttribute = html.getAttribute('lang') || '';
      const xmlLangAttribute = html.getAttribute('xml:lang') || '';

      // Content-based language detection (simplified heuristic)
      const textContent = document.body?.textContent || '';
      const words = textContent.toLowerCase().split(/\s+/).slice(0, 100); // First 100 words

      // Language detection heuristics
      const languagePatterns = {
        'en': ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'],
        'es': ['que', 'de', 'no', 'a', 'la', 'el', 'es', 'y', 'en', 'lo', 'un', 'por', 'qué', 'me', 'una', 'te', 'los', 'se', 'con', 'para', 'mi', 'está', 'si', 'bien', 'pero', 'yo', 'eso', 'las', 'sí', 'su', 'tu', 'aquí', 'del', 'al', 'como', 'le', 'más', 'esto', 'ya', 'todo', 'esta', 'vamos', 'muy', 'hay', 'ahora', 'algo', 'estoy', 'tengo', 'nos', 'tú', 'nada', 'cuando', 'ha', 'este', 'sé', 'estás', 'así', 'puedo', 'cómo', 'quiero', 'solo', 'soy', 'tiene', 'nos', 'ni', 'donde', 'él', 'estado', 'desde', 'todo', 'nos', 'durante', 'todos', 'uno', 'les', 'ni', 'contra', 'otros', 'ese', 'eso', 'había', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos', 'yo', 'otro', 'otras', 'otra', 'él', 'tanto', 'esa', 'estos', 'mucho', 'quienes', 'nada', 'muchos', 'cual', 'poco', 'ella', 'estar', 'estas', 'algunas', 'algo', 'nosotros', 'mi', 'mis', 'tú', 'te', 'ti', 'tu', 'tus', 'ellas', 'nosotras', 'vosotros', 'vosotras', 'os', 'mío', 'mía', 'míos', 'mías', 'tuyo', 'tuya', 'tuyos', 'tuyas', 'suyo', 'suya', 'suyos', 'suyas', 'nuestro', 'nuestra', 'nuestros', 'nuestras', 'vuestro', 'vuestra', 'vuestros', 'vuestras', 'esos', 'esas'],
        'fr': ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 'par', 'grand', 'en', 'une', 'être', 'et', 'en', 'avoir', 'que', 'pour'],
        'de': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im', 'dem', 'nicht', 'ein', 'eine', 'als', 'auch', 'es', 'an', 'werden', 'aus', 'er', 'hat', 'dass', 'sie', 'nach', 'wird', 'bei', 'einer', 'um', 'am', 'sind', 'noch', 'wie', 'einem', 'über', 'einen', 'so', 'zum', 'war', 'haben', 'nur', 'oder', 'aber', 'vor', 'zur', 'bis', 'mehr', 'durch', 'man', 'sein', 'wurde', 'sei', 'in', 'wenn', 'auch', 'ich', 'an', 'als', 'ja', 'wie', 'bei', 'oder', 'wir', 'aber', 'dann', 'ihn', 'wo', 'viel', 'nach', 'ohne', 'ihm', 'wieder', 'unter', 'vom', 'sehr', 'was', 'dort', 'alle', 'seiner', 'können', 'schon', 'hier', 'doch', 'da', 'er', 'diese', 'haben', 'ich', 'für', 'nicht', 'es', 'kann', 'sie', 'ist', 'einem', 'hatte', 'noch', 'werden', 'einen', 'welche', 'sind', 'dieser', 'wollen', 'dieses', 'machen', 'sollten', 'könnte', 'kaum', 'zur', 'seine', 'müssen', 'dabei', 'jedoch', 'während', 'ihnen', 'deren', 'deren', 'solche', 'weil', 'würde', 'etwa', 'gegen', 'weiter', 'nichts', 'anderer', 'allem', 'bekannt', 'bereits', 'ebenfalls', 'möglich', 'sowie', 'weitere'],
      };

      let detectedLanguage = 'unknown';
      let confidence = 0;
      let method: LanguageDetectionResult['method'] = 'heuristic';
      const supportingEvidence: string[] = [];
      const inconsistencies: string[] = [];

      // Method 1: Check lang attribute
      if (langAttribute) {
        detectedLanguage = langAttribute.split('-')[0]; // Get primary language code
        confidence = 0.9;
        method = 'attribute';
        supportingEvidence.push(`lang="${langAttribute}"`);
      }

      // Method 2: Check meta tags
      const metaLang = document.querySelector('meta[http-equiv="content-language"]')?.getAttribute('content');
      if (metaLang) {
        supportingEvidence.push(`meta content-language="${metaLang}"`);
        if (detectedLanguage === 'unknown') {
          detectedLanguage = metaLang.split('-')[0];
          confidence = 0.7;
          method = 'meta-tag';
        } else if (detectedLanguage !== metaLang.split('-')[0]) {
          inconsistencies.push(`lang attribute (${detectedLanguage}) differs from meta tag (${metaLang})`);
        }
      }

      // Method 3: Content analysis
      if (words.length > 10) {
        const languageScores: { [key: string]: number } = {};

        Object.entries(languagePatterns).forEach(([lang, patterns]) => {
          const matches = words.filter(word => patterns.includes(word)).length;
          languageScores[lang] = matches / words.length;
        });

        const topLanguage = Object.entries(languageScores)
          .sort(([,a], [,b]) => b - a)[0];

        if (topLanguage && topLanguage[1] > 0.1) {
          supportingEvidence.push(`content analysis suggests ${topLanguage[0]} (${(topLanguage[1] * 100).toFixed(1)}% match)`);

          if (detectedLanguage === 'unknown') {
            detectedLanguage = topLanguage[0];
            confidence = topLanguage[1] * 0.8; // Lower confidence for content analysis
            method = 'content-analysis';
          } else if (detectedLanguage !== topLanguage[0] && topLanguage[1] > 0.2) {
            inconsistencies.push(`declared language (${detectedLanguage}) differs from content analysis (${topLanguage[0]})`);
          }
        }
      }

      return {
        detectedLanguage,
        confidence,
        method,
        supportingEvidence,
        inconsistencies,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (languageDetection.detectedLanguage !== 'unknown' && languageDetection.confidence >= 0.7) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Advanced language detection: Language successfully detected',
        value: `Language: ${languageDetection.detectedLanguage}, Confidence: ${(languageDetection.confidence * 100).toFixed(1)}%, Method: ${languageDetection.method}`,
        severity: 'info',
      });

      if (languageDetection.supportingEvidence.length > 0) {
        evidence.push({
          type: 'text',
          description: 'Language detection supporting evidence',
          value: languageDetection.supportingEvidence.join('; '),
          severity: 'info',
        });
      }
    } else {
      issues.push('Language detection failed or low confidence');
      evidence.push({
        type: 'code',
        description: 'Advanced language detection: Failed to detect language reliably',
        value: `Detected: ${languageDetection.detectedLanguage}, Confidence: ${(languageDetection.confidence * 100).toFixed(1)}%`,
        severity: 'error',
      });
      recommendations.push('Add or improve lang attribute on html element');
    }

    // Report inconsistencies as warnings
    languageDetection.inconsistencies.forEach((inconsistency) => {
      issues.push(`Language inconsistency: ${inconsistency}`);
      evidence.push({
        type: 'code',
        description: 'Language inconsistency detected',
        value: inconsistency,
        severity: 'warning',
      });
      recommendations.push('Ensure consistent language declaration across all methods');
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Lang Attribute Validation Algorithm
   */
  private async validateLangAttribute(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const langValidation = await page.evaluate((): LangAttributeValidation => {
      const html = document.documentElement;
      const langValue = html.getAttribute('lang') || '';

      // ISO 639-1 language codes (simplified list)
      const validLanguageCodes = [
        'aa', 'ab', 'ae', 'af', 'ak', 'am', 'an', 'ar', 'as', 'av', 'ay', 'az',
        'ba', 'be', 'bg', 'bh', 'bi', 'bm', 'bn', 'bo', 'br', 'bs',
        'ca', 'ce', 'ch', 'co', 'cr', 'cs', 'cu', 'cv', 'cy',
        'da', 'de', 'dv', 'dz',
        'ee', 'el', 'en', 'eo', 'es', 'et', 'eu',
        'fa', 'ff', 'fi', 'fj', 'fo', 'fr', 'fy',
        'ga', 'gd', 'gl', 'gn', 'gu', 'gv',
        'ha', 'he', 'hi', 'ho', 'hr', 'ht', 'hu', 'hy', 'hz',
        'ia', 'id', 'ie', 'ig', 'ii', 'ik', 'io', 'is', 'it', 'iu',
        'ja', 'jv',
        'ka', 'kg', 'ki', 'kj', 'kk', 'kl', 'km', 'kn', 'ko', 'kr', 'ks', 'ku', 'kv', 'kw', 'ky',
        'la', 'lb', 'lg', 'li', 'ln', 'lo', 'lt', 'lu', 'lv',
        'mg', 'mh', 'mi', 'mk', 'ml', 'mn', 'mr', 'ms', 'mt', 'my',
        'na', 'nb', 'nd', 'ne', 'ng', 'nl', 'nn', 'no', 'nr', 'nv', 'ny',
        'oc', 'oj', 'om', 'or', 'os',
        'pa', 'pi', 'pl', 'ps', 'pt',
        'qu',
        'rm', 'rn', 'ro', 'ru', 'rw',
        'sa', 'sc', 'sd', 'se', 'sg', 'si', 'sk', 'sl', 'sm', 'sn', 'so', 'sq', 'sr', 'ss', 'st', 'su', 'sv', 'sw',
        'ta', 'te', 'tg', 'th', 'ti', 'tk', 'tl', 'tn', 'to', 'tr', 'ts', 'tt', 'tw', 'ty',
        'ug', 'uk', 'ur', 'uz',
        've', 'vi', 'vo',
        'wa', 'wo',
        'xh',
        'yi', 'yo',
        'za', 'zh', 'zu'
      ];

      const hasLangAttribute = html.hasAttribute('lang');
      const primaryLanguage = langValue.split('-')[0].toLowerCase();
      const isValidLanguageCode = validLanguageCodes.includes(primaryLanguage);
      const isISO639Compliant = /^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{1,8})*$/.test(langValue);
      const hasRegionCode = langValue.includes('-') && langValue.split('-').length >= 2;

      let attributeLocation: LangAttributeValidation['attributeLocation'] = 'missing';
      if (hasLangAttribute) {
        attributeLocation = 'html';
      } else if (document.body?.hasAttribute('lang')) {
        attributeLocation = 'body';
      }

      return {
        hasLangAttribute,
        langValue,
        isValidLanguageCode,
        isISO639Compliant,
        hasRegionCode,
        attributeLocation,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (langValidation.hasLangAttribute && langValidation.isValidLanguageCode && langValidation.isISO639Compliant) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Lang attribute validation: Valid language declaration',
        value: `lang="${langValidation.langValue}" - valid ISO 639 compliant language code`,
        severity: 'info',
      });
    } else {
      if (!langValidation.hasLangAttribute) {
        issues.push('Missing lang attribute on html element');
        evidence.push({
          type: 'code',
          description: 'Lang attribute validation: Missing lang attribute',
          value: `attributeLocation: ${langValidation.attributeLocation}`,
          severity: 'error',
        });
        recommendations.push('Add lang attribute to html element');
      } else if (!langValidation.isValidLanguageCode) {
        issues.push('Invalid language code in lang attribute');
        evidence.push({
          type: 'code',
          description: 'Lang attribute validation: Invalid language code',
          value: `lang="${langValidation.langValue}" - not a valid ISO 639 language code`,
          severity: 'error',
        });
        recommendations.push('Use a valid ISO 639 language code');
      } else if (!langValidation.isISO639Compliant) {
        issues.push('Lang attribute format is not ISO 639 compliant');
        evidence.push({
          type: 'code',
          description: 'Lang attribute validation: Format not compliant',
          value: `lang="${langValidation.langValue}" - format should be language-region (e.g., en-US)`,
          severity: 'warning',
        });
        recommendations.push('Use proper ISO 639 format for lang attribute');
      }
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Multilingual Content Analysis Algorithm
   */
  private async analyzeMultilingualContent(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const multilingualAnalysis = await page.evaluate((): MultilingualContentAnalysis => {
      const elementsWithLang = Array.from(document.querySelectorAll('[lang]'));
      const languageMap: { [key: string]: number } = {};
      const mixedLanguageElements: string[] = [];

      // Analyze language distribution
      elementsWithLang.forEach((element, index) => {
        const lang = element.getAttribute('lang') || '';
        const primaryLang = lang.split('-')[0];
        languageMap[primaryLang] = (languageMap[primaryLang] || 0) + 1;

        if (element !== document.documentElement) {
          mixedLanguageElements.push(`${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`);
        }
      });

      const languages = Object.keys(languageMap);
      const primaryLanguage = languages.reduce((a, b) =>
        languageMap[a] > languageMap[b] ? a : b, languages[0] || 'unknown');
      const secondaryLanguages = languages.filter(lang => lang !== primaryLanguage);

      // Check for language navigation
      const hasLanguageNavigation = document.querySelector(
        '[hreflang], .language-selector, .lang-switcher, [aria-label*="language"], [aria-label*="Language"]'
      ) !== null;

      // Calculate content consistency
      const totalElements = elementsWithLang.length;
      const primaryCount = languageMap[primaryLanguage] || 0;
      const contentLanguageConsistency = totalElements > 0 ? primaryCount / totalElements : 1;

      return {
        primaryLanguage,
        secondaryLanguages,
        languageSwitches: secondaryLanguages.length,
        hasLanguageNavigation,
        contentLanguageConsistency,
        mixedLanguageElements,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (multilingualAnalysis.languageSwitches === 0 ||
        (multilingualAnalysis.languageSwitches > 0 && multilingualAnalysis.hasLanguageNavigation)) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Multilingual content analysis: Proper language handling',
        value: `Primary: ${multilingualAnalysis.primaryLanguage}, Secondary: ${multilingualAnalysis.secondaryLanguages.length}, Navigation: ${multilingualAnalysis.hasLanguageNavigation}`,
        severity: 'info',
      });
    } else {
      issues.push('Multilingual content detected without proper navigation');
      evidence.push({
        type: 'code',
        description: 'Multilingual content analysis: Missing language navigation',
        value: `${multilingualAnalysis.languageSwitches} secondary languages detected without navigation`,
        severity: 'warning',
      });
      recommendations.push('Add language navigation for multilingual content');
    }

    // Report mixed language elements
    if (multilingualAnalysis.mixedLanguageElements.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Mixed language elements detected',
        value: `${multilingualAnalysis.mixedLanguageElements.length} elements with different lang attributes`,
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Language Consistency Testing Algorithm
   */
  private async testLanguageConsistency(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const consistencyAnalysis = await page.evaluate(() => {
      const htmlLang = document.documentElement.getAttribute('lang') || '';
      const metaLang = document.querySelector('meta[http-equiv="content-language"]')?.getAttribute('content') || '';
      const xmlLang = document.documentElement.getAttribute('xml:lang') || '';

      // Check for inconsistencies
      const inconsistencies: string[] = [];

      if (htmlLang && metaLang && htmlLang.split('-')[0] !== metaLang.split('-')[0]) {
        inconsistencies.push(`HTML lang (${htmlLang}) differs from meta content-language (${metaLang})`);
      }

      if (htmlLang && xmlLang && htmlLang !== xmlLang) {
        inconsistencies.push(`HTML lang (${htmlLang}) differs from xml:lang (${xmlLang})`);
      }

      // Check for conflicting lang attributes in content
      const elementsWithLang = Array.from(document.querySelectorAll('[lang]'));
      const conflictingElements = elementsWithLang.filter(el => {
        const elementLang = el.getAttribute('lang') || '';
        return elementLang && elementLang.split('-')[0] !== htmlLang.split('-')[0] && el !== document.documentElement;
      });

      return {
        htmlLang,
        metaLang,
        xmlLang,
        inconsistencies,
        conflictingElementsCount: conflictingElements.length,
        hasConsistentDeclaration: inconsistencies.length === 0,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (consistencyAnalysis.hasConsistentDeclaration) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Language consistency testing: Consistent language declarations',
        value: `HTML lang: ${consistencyAnalysis.htmlLang}, Meta: ${consistencyAnalysis.metaLang || 'none'}, XML: ${consistencyAnalysis.xmlLang || 'none'}`,
        severity: 'info',
      });
    } else {
      issues.push('Inconsistent language declarations detected');
      evidence.push({
        type: 'code',
        description: 'Language consistency testing: Inconsistencies found',
        value: consistencyAnalysis.inconsistencies.join('; '),
        severity: 'warning',
      });
      recommendations.push('Ensure consistent language declarations across all methods');
    }

    // Report conflicting elements
    if (consistencyAnalysis.conflictingElementsCount > 0) {
      evidence.push({
        type: 'text',
        description: 'Elements with different language declarations',
        value: `${consistencyAnalysis.conflictingElementsCount} elements with conflicting lang attributes`,
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  private getCommonLanguageCodes(): string[] {
    return [
      'en', 'en-US', 'en-GB', 'en-CA', 'en-AU',
      'es', 'es-ES', 'es-MX', 'es-AR',
      'fr', 'fr-FR', 'fr-CA',
      'de', 'de-DE', 'de-AT', 'de-CH',
      'it', 'it-IT',
      'pt', 'pt-BR', 'pt-PT',
      'ru', 'ru-RU',
      'ja', 'ja-JP',
      'ko', 'ko-KR',
      'zh', 'zh-CN', 'zh-TW', 'zh-HK',
      'ar', 'ar-SA', 'ar-EG',
      'hi', 'hi-IN',
      'th', 'th-TH',
      'vi', 'vi-VN',
      'nl', 'nl-NL', 'nl-BE',
      'sv', 'sv-SE',
      'da', 'da-DK',
      'no', 'no-NO', 'nb-NO', 'nn-NO',
      'fi', 'fi-FI',
      'pl', 'pl-PL',
      'cs', 'cs-CZ',
      'hu', 'hu-HU',
      'tr', 'tr-TR',
    ];
  }
}
      score = 0;
      issues.push('HTML document missing lang attribute');
      
      evidence.push({
        type: 'code',
        description: 'HTML element missing lang attribute',
        value: langAnalysis.htmlOuterHTML,
        selector: 'html',
        elementCount: 1,
        affectedSelectors: ['html'],
        severity: 'error',
        fixExample: {
          before: '<html>',
          after: '<html lang="en">',
          description: 'Add lang attribute to html element',
          codeExample: `
<!-- Before -->
<html>
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>

<!-- After -->
<html lang="en">
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            hasXmlLang: langAnalysis.hasXmlLang,
            xmlLangValue: langAnalysis.xmlLangValue || '',
          },
        },
      });
      
      recommendations.push('Add lang attribute to html element: <html lang="en">');
      recommendations.push('Use a valid ISO 639-1 language code');
      recommendations.push('Consider the primary language of your content');
      
    } else {
      // Validate lang code format
      const langCode = langAnalysis.langValue.toLowerCase();
      const validLangPattern = /^[a-z]{2,3}(-[a-z]{2,4})*$/i;
      
      if (!validLangPattern.test(langCode)) {
        score = 0;
        issues.push(`Invalid language code: ${langAnalysis.langValue}`);
        
        evidence.push({
          type: 'code',
          description: 'Invalid language code format',
          value: `<html lang="${langAnalysis.langValue}">`,
          selector: 'html',
          elementCount: 1,
          affectedSelectors: ['html'],
          severity: 'error',
          fixExample: {
            before: `<html lang="${langAnalysis.langValue}">`,
            after: '<html lang="en">',
            description: 'Use valid ISO language code',
            resources: [
              'https://www.w3.org/International/questions/qa-choosing-language-tags',
              'https://www.iana.org/assignments/language-subtag-registry/language-subtag-registry'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              invalidLangCode: langAnalysis.langValue,
              suggestedCodes: ['en', 'en-US', 'fr', 'es', 'de'],
            },
          },
        });
        
        recommendations.push('Use valid ISO language code (e.g., "en", "en-US", "fr", "es")');
        recommendations.push('Check the IANA Language Subtag Registry for valid codes');
      } else {
        // Success case - add positive evidence
        evidence.push({
          type: 'info',
          description: 'HTML element has valid lang attribute',
          value: `<html lang="${langAnalysis.langValue}">`,
          selector: 'html',
          elementCount: 1,
          affectedSelectors: ['html'],
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              validLangCode: langAnalysis.langValue,
              hasXmlLang: langAnalysis.hasXmlLang,
              xmlLangValue: langAnalysis.xmlLangValue || '',
            },
          },
        });
        
        recommendations.push('Continue using valid language codes for all content');
        if (!langAnalysis.hasXmlLang) {
          recommendations.push('Consider adding xml:lang attribute for XHTML compatibility');
        }
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Get common language codes for validation
   */
  private getCommonLanguageCodes(): string[] {
    return [
      'en', 'en-US', 'en-GB', 'en-CA', 'en-AU',
      'es', 'es-ES', 'es-MX', 'es-AR',
      'fr', 'fr-FR', 'fr-CA',
      'de', 'de-DE', 'de-AT', 'de-CH',
      'it', 'it-IT',
      'pt', 'pt-BR', 'pt-PT',
      'ru', 'ru-RU',
      'ja', 'ja-JP',
      'ko', 'ko-KR',
      'zh', 'zh-CN', 'zh-TW',
      'ar', 'ar-SA',
      'hi', 'hi-IN',
      'nl', 'nl-NL',
      'sv', 'sv-SE',
      'no', 'no-NO',
      'da', 'da-DK',
      'fi', 'fi-FI',
      'pl', 'pl-PL',
      'tr', 'tr-TR',
    ];
  }
}
