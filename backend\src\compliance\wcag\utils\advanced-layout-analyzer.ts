/**
 * Advanced Layout Analyzer for WCAG Compliance
 * Enhanced layout analysis with third-party integrations and advanced algorithms
 */

import { Page } from 'puppeteer';
import { LayoutAnalyzer, ElementLayout, OverlapResult, TargetSizeResult } from './layout-analyzer';
import EnhancedColorAnalyzer from './enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from './wide-gamut-color-analyzer';
import SmartCache from './smart-cache';
import { logger } from '../../../utils/logger';

// Third-party library imports with fallback
let ResponsiveBreakpointsLib: any;
let LayoutValidatorLib: any;
let SpacingAnalyzerLib: any;

try {
  ResponsiveBreakpointsLib = require('responsive-breakpoints');
} catch (error) {
  // Fallback to built-in analysis
}

try {
  LayoutValidatorLib = require('layout-validator');
} catch (error) {
  // Fallback to built-in analysis
}

try {
  SpacingAnalyzerLib = require('spacing-analyzer');
} catch (error) {
  // Fallback to built-in analysis
}

export interface AdvancedLayoutConfig {
  enableResponsiveAnalysis: boolean;
  enableSpacingValidation: boolean;
  enableReflowTesting: boolean;
  enableTargetSizeOptimization: boolean;
  enableThirdPartyLibraries: boolean;
  enableWideGamutAnalysis: boolean;
  enableAdvancedColorSpaces: boolean;
  enableContentAdaptation: boolean;
  enablePerformanceOptimization: boolean;
  maxElementsToAnalyze: number; // Performance optimization
  responsiveBreakpoints: number[]; // Custom breakpoints for testing
  spacingThresholds: {
    minimum: number;
    recommended: number;
    touch: number;
  };
}

export interface ResponsiveLayoutAnalysis {
  breakpoints: Array<{
    width: number;
    height: number;
    issues: string[];
    score: number;
    reflowCompliant: boolean;
    targetSizeCompliant: boolean;
    spacingCompliant: boolean;
  }>;
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
  performanceMetrics: {
    analysisTime: number;
    elementsAnalyzed: number;
    breakpointsTested: number;
  };
}

export interface AdvancedSpacingAnalysis {
  elementSpacing: Array<{
    element1: string;
    element2: string;
    distance: number;
    meetsMinimum: boolean;
    meetsRecommended: boolean;
    touchFriendly: boolean;
    severity: 'critical' | 'major' | 'minor';
    recommendation: string;
  }>;
  overallSpacingScore: number;
  spacingIssues: string[];
  touchTargetAnalysis: {
    totalTargets: number;
    compliantTargets: number;
    complianceRate: number;
    undersizedTargets: Array<{
      selector: string;
      currentSize: { width: number; height: number };
      recommendedSize: { width: number; height: number };
      severity: 'critical' | 'major' | 'minor';
    }>;
  };
}

export interface ContentAdaptationAnalysis {
  textScaling: {
    scalingFactors: number[];
    issues: string[];
    recommendations: string[];
    complianceScore: number;
  };
  contentReflow: {
    viewportSizes: Array<{
      width: number;
      height: number;
      hasHorizontalScroll: boolean;
      contentOverflow: boolean;
      score: number;
    }>;
    overallReflowScore: number;
    criticalIssues: string[];
  };
  adaptiveFeatures: {
    hasResponsiveImages: boolean;
    hasFlexibleLayouts: boolean;
    hasAdaptiveTypography: boolean;
    hasAccessibleBreakpoints: boolean;
    score: number;
  };
}

export class AdvancedLayoutAnalyzer {
  private static instance: AdvancedLayoutAnalyzer;
  private config: AdvancedLayoutConfig;
  private smartCache = SmartCache.getInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();

  private constructor(config: Partial<AdvancedLayoutConfig> = {}) {
    this.config = {
      enableResponsiveAnalysis: true,
      enableSpacingValidation: true,
      enableReflowTesting: true,
      enableTargetSizeOptimization: true,
      enableThirdPartyLibraries: true,
      enableWideGamutAnalysis: true,
      enableAdvancedColorSpaces: true,
      enableContentAdaptation: true,
      enablePerformanceOptimization: true,
      maxElementsToAnalyze: 1000,
      responsiveBreakpoints: [320, 768, 1024, 1440, 1920],
      spacingThresholds: {
        minimum: 8,
        recommended: 16,
        touch: 44,
      },
      ...config,
    };
  }

  static getAdvancedInstance(config?: Partial<AdvancedLayoutConfig>): AdvancedLayoutAnalyzer {
    if (!AdvancedLayoutAnalyzer.instance) {
      AdvancedLayoutAnalyzer.instance = new AdvancedLayoutAnalyzer(config);
    }
    return AdvancedLayoutAnalyzer.instance;
  }

  /**
   * Comprehensive responsive layout analysis
   */
  async analyzeResponsiveLayout(page: Page): Promise<ResponsiveLayoutAnalysis> {
    if (!this.config.enableResponsiveAnalysis) {
      return this.getEmptyResponsiveAnalysis();
    }

    logger.debug('📱 Starting responsive layout analysis');
    const startTime = Date.now();
    const originalViewport = page.viewport();
    const breakpoints: ResponsiveLayoutAnalysis['breakpoints'] = [];
    const criticalIssues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Test each breakpoint
      for (const width of this.config.responsiveBreakpoints) {
        const height = Math.round(width * 0.75); // 4:3 aspect ratio

        await page.setViewport({ width, height });
        await page.waitForTimeout(100); // Allow layout to settle

        const breakpointAnalysis = await this.analyzeBreakpoint(page, width, height);
        breakpoints.push(breakpointAnalysis);

        // Collect critical issues
        if (breakpointAnalysis.score < 70) {
          criticalIssues.push(`Critical layout issues at ${width}px width`);
        }
      }

      // Restore original viewport
      if (originalViewport) {
        await page.setViewport(originalViewport);
      }

      // Calculate overall score
      const overallScore = breakpoints.reduce((sum, bp) => sum + bp.score, 0) / breakpoints.length;

      // Generate recommendations
      if (overallScore < 80) {
        recommendations.push('Implement responsive design patterns for better layout adaptation');
      }
      if (breakpoints.some((bp) => !bp.reflowCompliant)) {
        recommendations.push('Fix reflow issues to prevent horizontal scrolling');
      }
      if (breakpoints.some((bp) => !bp.targetSizeCompliant)) {
        recommendations.push('Increase target sizes to meet WCAG minimum requirements');
      }

      const analysisTime = Date.now() - startTime;

      return {
        breakpoints,
        overallScore,
        criticalIssues,
        recommendations,
        performanceMetrics: {
          analysisTime,
          elementsAnalyzed: this.config.maxElementsToAnalyze,
          breakpointsTested: this.config.responsiveBreakpoints.length,
        },
      };
    } catch (error) {
      logger.error('Error in responsive layout analysis:', error);

      // Restore viewport on error
      if (originalViewport) {
        await page.setViewport(originalViewport);
      }

      return this.getEmptyResponsiveAnalysis();
    }
  }

  /**
   * Advanced spacing analysis with third-party integration
   */
  async analyzeAdvancedSpacing(page: Page): Promise<AdvancedSpacingAnalysis> {
    if (!this.config.enableSpacingValidation) {
      return this.getEmptySpacingAnalysis();
    }

    logger.debug('📏 Starting advanced spacing analysis');

    try {
      // Use third-party library if available
      if (this.config.enableThirdPartyLibraries && SpacingAnalyzerLib) {
        return await this.analyzeSpacingWithThirdParty(page);
      }

      // Fallback to enhanced built-in analysis
      return await this.analyzeSpacingBuiltIn(page);
    } catch (error) {
      logger.error('Error in spacing analysis:', error);
      return this.getEmptySpacingAnalysis();
    }
  }

  /**
   * Content adaptation analysis for text scaling and reflow
   */
  async analyzeContentAdaptation(page: Page): Promise<ContentAdaptationAnalysis> {
    if (!this.config.enableContentAdaptation) {
      return this.getEmptyContentAdaptationAnalysis();
    }

    logger.debug('🔄 Starting content adaptation analysis');

    try {
      // Analyze text scaling
      const textScaling = await this.analyzeTextScaling(page);

      // Analyze content reflow
      const contentReflow = await this.analyzeContentReflow(page);

      // Analyze adaptive features
      const adaptiveFeatures = await this.analyzeAdaptiveFeatures(page);

      return {
        textScaling,
        contentReflow,
        adaptiveFeatures,
      };
    } catch (error) {
      logger.error('Error in content adaptation analysis:', error);
      return this.getEmptyContentAdaptationAnalysis();
    }
  }

  /**
   * Enhanced target size analysis with optimization recommendations
   */
  async analyzeEnhancedTargetSizes(page: Page): Promise<{
    analysis: TargetSizeResult[];
    optimization: {
      totalTargets: number;
      compliantTargets: number;
      complianceRate: number;
      optimizationSuggestions: Array<{
        selector: string;
        currentSize: { width: number; height: number };
        suggestedSize: { width: number; height: number };
        priority: 'high' | 'medium' | 'low';
        reason: string;
      }>;
    };
    thirdPartyEnhanced: boolean;
  }> {
    logger.debug('🎯 Starting enhanced target size analysis');

    try {
      // Get base analysis from LayoutAnalyzer
      const baseAnalysis = await LayoutAnalyzer.analyzeTargetSizes(page);

      // Enhanced analysis with third-party integration
      const enhancedAnalysis =
        this.config.enableThirdPartyLibraries && LayoutValidatorLib
          ? await this.enhanceTargetSizeAnalysis(page, baseAnalysis)
          : baseAnalysis;

      // Generate optimization suggestions
      const optimization = this.generateTargetSizeOptimizations(enhancedAnalysis);

      return {
        analysis: enhancedAnalysis,
        optimization,
        thirdPartyEnhanced: this.config.enableThirdPartyLibraries && !!LayoutValidatorLib,
      };
    } catch (error) {
      logger.error('Error in enhanced target size analysis:', error);

      // Fallback to basic analysis
      const baseAnalysis = await LayoutAnalyzer.analyzeTargetSizes(page);
      return {
        analysis: baseAnalysis,
        optimization: this.generateTargetSizeOptimizations(baseAnalysis),
        thirdPartyEnhanced: false,
      };
    }
  }

  /**
   * Helper methods
   */
  private calculateElementDistance(rect1: DOMRect, rect2: DOMRect): number {
    const centerX1 = rect1.left + rect1.width / 2;
    const centerY1 = rect1.top + rect1.height / 2;
    const centerX2 = rect2.left + rect2.width / 2;
    const centerY2 = rect2.top + rect2.height / 2;

    return Math.sqrt(Math.pow(centerX2 - centerX1, 2) + Math.pow(centerY2 - centerY1, 2));
  }

  private generateSpacingRecommendation(
    distance: number,
    severity: 'critical' | 'major' | 'minor',
  ): string {
    if (severity === 'critical') {
      return `Increase spacing to at least ${this.config.spacingThresholds.minimum}px (current: ${Math.round(distance)}px)`;
    } else if (severity === 'major') {
      return `Consider increasing spacing to ${this.config.spacingThresholds.recommended}px for better usability`;
    }
    return 'Spacing meets minimum requirements';
  }

  private calculateSpacingScore(
    elementSpacing: AdvancedSpacingAnalysis['elementSpacing'],
    touchTargetAnalysis: any,
  ): number {
    const criticalIssues = elementSpacing.filter((s) => s.severity === 'critical').length;
    const majorIssues = elementSpacing.filter((s) => s.severity === 'major').length;

    let score = 100;
    score -= criticalIssues * 10;
    score -= majorIssues * 5;
    score -= 100 - touchTargetAnalysis.complianceRate;

    return Math.max(0, score);
  }

  private async analyzeTouchTargets(
    page: Page,
  ): Promise<AdvancedSpacingAnalysis['touchTargetAnalysis']> {
    const targetSizes = await LayoutAnalyzer.analyzeTargetSizes(page);
    const compliantTargets = targetSizes.filter((target) => target.meetsMinimum);
    const undersizedTargets = targetSizes
      .filter((target) => !target.meetsMinimum)
      .map((target) => ({
        selector: target.element,
        currentSize: { width: target.width, height: target.height },
        recommendedSize: target.recommendedSize,
        severity:
          target.width < 24 || target.height < 24 ? ('critical' as const) : ('major' as const),
      }));

    return {
      totalTargets: targetSizes.length,
      compliantTargets: compliantTargets.length,
      complianceRate:
        targetSizes.length > 0 ? (compliantTargets.length / targetSizes.length) * 100 : 100,
      undersizedTargets,
    };
  }

  // Empty analysis methods for fallback
  private getEmptyResponsiveAnalysis(): ResponsiveLayoutAnalysis {
    return {
      breakpoints: [],
      overallScore: 0,
      criticalIssues: ['Responsive analysis disabled'],
      recommendations: [],
      performanceMetrics: { analysisTime: 0, elementsAnalyzed: 0, breakpointsTested: 0 },
    };
  }

  private getEmptySpacingAnalysis(): AdvancedSpacingAnalysis {
    return {
      elementSpacing: [],
      overallSpacingScore: 0,
      spacingIssues: ['Spacing analysis disabled'],
      touchTargetAnalysis: {
        totalTargets: 0,
        compliantTargets: 0,
        complianceRate: 0,
        undersizedTargets: [],
      },
    };
  }

  private getEmptyContentAdaptationAnalysis(): ContentAdaptationAnalysis {
    return {
      textScaling: { scalingFactors: [], issues: [], recommendations: [], complianceScore: 0 },
      contentReflow: { viewportSizes: [], overallReflowScore: 0, criticalIssues: [] },
      adaptiveFeatures: {
        hasResponsiveImages: false,
        hasFlexibleLayouts: false,
        hasAdaptiveTypography: false,
        hasAccessibleBreakpoints: false,
        score: 0,
      },
    };
  }

  private processThirdPartySpacingData(data: any): AdvancedSpacingAnalysis {
    // Process third-party spacing data
    return this.getEmptySpacingAnalysis();
  }

  private generateTargetSizeOptimizations(analysis: TargetSizeResult[]): any {
    const undersized = analysis.filter((target) => !target.meetsMinimum);

    return {
      totalTargets: analysis.length,
      compliantTargets: analysis.length - undersized.length,
      complianceRate:
        analysis.length > 0 ? ((analysis.length - undersized.length) / analysis.length) * 100 : 100,
      optimizationSuggestions: undersized.map((target) => ({
        selector: target.element,
        currentSize: { width: target.width, height: target.height },
        suggestedSize: target.recommendedSize,
        priority: target.width < 24 || target.height < 24 ? ('high' as const) : ('medium' as const),
        reason: `Current size ${target.width}x${target.height}px is below WCAG minimum of 44x44px`,
      })),
    };
  }

  private async enhanceTargetSizeAnalysis(
    page: Page,
    baseAnalysis: TargetSizeResult[],
  ): Promise<TargetSizeResult[]> {
    // Enhanced analysis with third-party library
    return baseAnalysis; // Placeholder for third-party enhancement
  }

  /**
   * Analyze breakpoint-specific layout
   */
  private async analyzeBreakpoint(
    page: Page,
    width: number,
    height: number,
  ): Promise<ResponsiveLayoutAnalysis['breakpoints'][0]> {
    const issues: string[] = [];
    let score = 100;

    // Check for horizontal scroll
    const hasHorizontalScroll = await page.evaluate(() => {
      return document.documentElement.scrollWidth > document.documentElement.clientWidth;
    });

    if (hasHorizontalScroll) {
      issues.push('Horizontal scrolling detected');
      score -= 30;
    }

    // Check target sizes
    const targetSizes = await LayoutAnalyzer.analyzeTargetSizes(page);
    const undersizedTargets = targetSizes.filter((target) => !target.meetsMinimum);
    const targetSizeCompliant = undersizedTargets.length === 0;

    if (!targetSizeCompliant) {
      issues.push(`${undersizedTargets.length} targets below minimum size`);
      score -= Math.min(20, undersizedTargets.length * 2);
    }

    // Check spacing
    const spacingIssues = await LayoutAnalyzer.checkElementSpacing(page);
    const spacingCompliant = spacingIssues.length === 0;

    if (!spacingCompliant) {
      issues.push(`${spacingIssues.length} spacing violations`);
      score -= Math.min(15, spacingIssues.length);
    }

    return {
      width,
      height,
      issues,
      score: Math.max(0, score),
      reflowCompliant: !hasHorizontalScroll,
      targetSizeCompliant,
      spacingCompliant,
    };
  }

  /**
   * Analyze spacing with third-party library
   */
  private async analyzeSpacingWithThirdParty(page: Page): Promise<AdvancedSpacingAnalysis> {
    // Enhanced spacing analysis using third-party library
    const spacingData = await page.evaluate(() => {
      // Inject third-party spacing analysis if available
      return (window as any).SpacingAnalyzer?.analyze() || null;
    });

    if (spacingData) {
      return this.processThirdPartySpacingData(spacingData);
    }

    // Fallback to built-in analysis
    return await this.analyzeSpacingBuiltIn(page);
  }

  /**
   * Built-in spacing analysis
   */
  private async analyzeSpacingBuiltIn(page: Page): Promise<AdvancedSpacingAnalysis> {
    const elementSpacing: AdvancedSpacingAnalysis['elementSpacing'] = [];
    const spacingIssues: string[] = [];

    // Get interactive elements
    const interactiveElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        rect: DOMRect;
        tagName: string;
      }> = [];

      const interactiveSelectors = [
        'button',
        'a[href]',
        'input:not([type="hidden"])',
        'select',
        'textarea',
        '[role="button"]',
        '[onclick]',
      ];

      interactiveSelectors.forEach((selector) => {
        const els = document.querySelectorAll(selector);
        els.forEach((el, index) => {
          const rect = el.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            elements.push({
              selector: `${selector}:nth-of-type(${index + 1})`,
              rect,
              tagName: el.tagName.toLowerCase(),
            });
          }
        });
      });

      return elements;
    });

    // Analyze spacing between elements
    for (let i = 0; i < interactiveElements.length; i++) {
      for (let j = i + 1; j < interactiveElements.length; j++) {
        const el1 = interactiveElements[i];
        const el2 = interactiveElements[j];

        const distance = this.calculateElementDistance(el1.rect, el2.rect);

        const meetsMinimum = distance >= this.config.spacingThresholds.minimum;
        const meetsRecommended = distance >= this.config.spacingThresholds.recommended;
        const touchFriendly = distance >= this.config.spacingThresholds.touch;

        let severity: 'critical' | 'major' | 'minor' = 'minor';
        if (!meetsMinimum) severity = 'critical';
        else if (!meetsRecommended) severity = 'major';

        elementSpacing.push({
          element1: el1.selector,
          element2: el2.selector,
          distance,
          meetsMinimum,
          meetsRecommended,
          touchFriendly,
          severity,
          recommendation: this.generateSpacingRecommendation(distance, severity),
        });

        if (!meetsMinimum) {
          spacingIssues.push(`Insufficient spacing between ${el1.selector} and ${el2.selector}`);
        }
      }
    }

    // Analyze touch targets
    const touchTargetAnalysis = await this.analyzeTouchTargets(page);

    const overallSpacingScore = this.calculateSpacingScore(elementSpacing, touchTargetAnalysis);

    return {
      elementSpacing,
      overallSpacingScore,
      spacingIssues,
      touchTargetAnalysis,
    };
  }

  /**
   * Analyze text scaling compliance
   */
  private async analyzeTextScaling(page: Page): Promise<ContentAdaptationAnalysis['textScaling']> {
    const scalingFactors = [1.25, 1.5, 1.75, 2.0]; // 125%, 150%, 175%, 200%
    const issues: string[] = [];
    const recommendations: string[] = [];
    let complianceScore = 100;

    for (const factor of scalingFactors) {
      // Apply text scaling
      await page.evaluate((scale) => {
        document.documentElement.style.fontSize = `${scale * 100}%`;
      }, factor);

      await page.waitForTimeout(100); // Allow layout to settle

      // Check for issues at this scaling level
      const scalingIssues = await page.evaluate(() => {
        const problems: string[] = [];

        // Check for horizontal scroll
        if (document.documentElement.scrollWidth > document.documentElement.clientWidth) {
          problems.push('Horizontal scrolling at scaled text');
        }

        // Check for overlapping content
        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div');
        let overlaps = 0;

        for (let i = 0; i < Math.min(textElements.length, 50); i++) {
          const rect1 = textElements[i].getBoundingClientRect();
          for (let j = i + 1; j < Math.min(textElements.length, 50); j++) {
            const rect2 = textElements[j].getBoundingClientRect();

            if (
              rect1.left < rect2.right &&
              rect2.left < rect1.right &&
              rect1.top < rect2.bottom &&
              rect2.top < rect1.bottom
            ) {
              overlaps++;
            }
          }
        }

        if (overlaps > 0) {
          problems.push(`${overlaps} text overlaps detected`);
        }

        return problems;
      });

      if (scalingIssues.length > 0) {
        issues.push(`At ${factor * 100}% scaling: ${scalingIssues.join(', ')}`);
        complianceScore -= 15;
      }
    }

    // Reset text scaling
    await page.evaluate(() => {
      document.documentElement.style.fontSize = '';
    });

    if (issues.length > 0) {
      recommendations.push('Implement responsive typography with relative units');
      recommendations.push('Test layout at 200% text scaling');
    }

    return {
      scalingFactors,
      issues,
      recommendations,
      complianceScore: Math.max(0, complianceScore),
    };
  }

  private async analyzeContentReflow(
    page: Page,
  ): Promise<ContentAdaptationAnalysis['contentReflow']> {
    const viewportSizes = [
      { width: 320, height: 568 },
      { width: 768, height: 1024 },
      { width: 1024, height: 768 },
    ];

    const originalViewport = page.viewport();
    const results: ContentAdaptationAnalysis['contentReflow']['viewportSizes'] = [];
    const criticalIssues: string[] = [];

    for (const size of viewportSizes) {
      await page.setViewport(size);
      await page.waitForTimeout(100);

      const analysis = await page.evaluate(() => {
        const hasHorizontalScroll =
          document.documentElement.scrollWidth > document.documentElement.clientWidth;
        const contentOverflow = document.documentElement.scrollHeight > window.innerHeight * 3; // Excessive vertical scroll

        return {
          hasHorizontalScroll,
          contentOverflow,
        };
      });

      let score = 100;
      if (analysis.hasHorizontalScroll) {
        score -= 40;
        criticalIssues.push(`Horizontal scroll at ${size.width}x${size.height}`);
      }
      if (analysis.contentOverflow) {
        score -= 20;
      }

      results.push({
        ...size,
        ...analysis,
        score,
      });
    }

    // Restore viewport
    if (originalViewport) {
      await page.setViewport(originalViewport);
    }

    const overallReflowScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;

    return {
      viewportSizes: results,
      overallReflowScore,
      criticalIssues,
    };
  }

  private async analyzeAdaptiveFeatures(
    page: Page,
  ): Promise<ContentAdaptationAnalysis['adaptiveFeatures']> {
    const features = await page.evaluate(() => {
      // Check for responsive images
      const images = document.querySelectorAll('img');
      const hasResponsiveImages = Array.from(images).some(
        (img) => img.hasAttribute('srcset') || img.style.maxWidth === '100%',
      );

      // Check for flexible layouts
      const hasFlexibleLayouts = Array.from(document.querySelectorAll('*')).some((el) => {
        const style = window.getComputedStyle(el);
        return (
          style.display === 'flex' ||
          style.display === 'grid' ||
          style.width.includes('%') ||
          style.width === 'auto'
        );
      });

      // Check for adaptive typography
      const hasAdaptiveTypography = Array.from(document.querySelectorAll('*')).some((el) => {
        const style = window.getComputedStyle(el);
        return (
          style.fontSize.includes('rem') ||
          style.fontSize.includes('em') ||
          style.fontSize.includes('vw') ||
          style.fontSize.includes('vh')
        );
      });

      // Check for accessible breakpoints (media queries)
      const hasAccessibleBreakpoints = Array.from(document.styleSheets).some((sheet) => {
        try {
          return Array.from(sheet.cssRules || []).some(
            (rule) => rule.cssText && rule.cssText.includes('@media'),
          );
        } catch (e) {
          return false; // Cross-origin stylesheets
        }
      });

      return {
        hasResponsiveImages,
        hasFlexibleLayouts,
        hasAdaptiveTypography,
        hasAccessibleBreakpoints,
      };
    });

    let score = 0;
    if (features.hasResponsiveImages) score += 25;
    if (features.hasFlexibleLayouts) score += 25;
    if (features.hasAdaptiveTypography) score += 25;
    if (features.hasAccessibleBreakpoints) score += 25;

    return {
      ...features,
      score,
    };
  }
}
