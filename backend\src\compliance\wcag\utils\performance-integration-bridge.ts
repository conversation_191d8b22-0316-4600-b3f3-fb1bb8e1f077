/**
 * Performance Integration Bridge
 * Connects UtilityIntegrationManager with existing performance monitoring systems
 */

import { UtilityIntegrationManager } from './utility-integration-manager';
import WCAGPerformanceMonitor, { UtilityPerformanceMetrics } from './performance-monitor';
import EnhancedPerformanceMonitor from './enhanced-performance-monitor';
import logger from '../../../utils/logger';

export interface PerformanceIntegrationConfig {
  enableUtilityTracking: boolean;
  enableRealTimeMetrics: boolean;
  enablePredictiveAnalytics: boolean;
  metricsCollectionInterval: number;
  performanceThresholds: {
    maxUtilityOverhead: number; // percentage
    minCacheHitRate: number; // percentage
    maxUtilityExecutionTime: number; // milliseconds
  };
}

export interface IntegratedPerformanceReport {
  scanId: string;
  timestamp: Date;
  overallMetrics: {
    totalDuration: number;
    checksExecuted: number;
    successRate: number;
    memoryPeakMB: number;
  };
  utilityMetrics: {
    totalUtilitiesUsed: number;
    averageCacheHitRate: number;
    averageUtilityOverhead: number;
    mostUsedUtilities: Array<{ utility: string; count: number }>;
    integrationStrategies: Record<string, number>;
    utilityErrors: string[];
  };
  performanceInsights: {
    recommendations: string[];
    optimizationOpportunities: string[];
    alerts: string[];
  };
  trends: {
    performanceImprovement: number;
    cacheEfficiencyTrend: number;
    utilityOptimizationScore: number;
  };
}

/**
 * Performance Integration Bridge Class
 */
export class PerformanceIntegrationBridge {
  private static instance: PerformanceIntegrationBridge;

  private utilityManager: UtilityIntegrationManager;
  private performanceMonitor: WCAGPerformanceMonitor;
  private enhancedMonitor: EnhancedPerformanceMonitor;

  private config: PerformanceIntegrationConfig;
  private metricsHistory: Map<string, IntegratedPerformanceReport[]> = new Map();

  private constructor() {
    this.utilityManager = UtilityIntegrationManager.getInstance();
    this.performanceMonitor = WCAGPerformanceMonitor.getInstance();
    this.enhancedMonitor = EnhancedPerformanceMonitor.getInstance();

    this.config = {
      enableUtilityTracking: true,
      enableRealTimeMetrics: true,
      enablePredictiveAnalytics: true,
      metricsCollectionInterval: 30000, // 30 seconds
      performanceThresholds: {
        maxUtilityOverhead: 25, // 25%
        minCacheHitRate: 70, // 70%
        maxUtilityExecutionTime: 5000, // 5 seconds
      },
    };

    this.initializeIntegration();
    logger.info('🔗 Performance Integration Bridge initialized');
  }

  static getInstance(): PerformanceIntegrationBridge {
    if (!PerformanceIntegrationBridge.instance) {
      PerformanceIntegrationBridge.instance = new PerformanceIntegrationBridge();
    }
    return PerformanceIntegrationBridge.instance;
  }

  /**
   * Initialize integration between systems
   */
  private initializeIntegration(): void {
    // Set up periodic metrics collection
    if (this.config.enableRealTimeMetrics) {
      setInterval(() => {
        this.collectRealTimeMetrics();
      }, this.config.metricsCollectionInterval);
    }
  }

  /**
   * Start integrated monitoring for a scan
   */
  startIntegratedMonitoring(scanId: string): void {
    // Start monitoring in all systems
    this.performanceMonitor.startScanMonitoring(scanId);
    this.enhancedMonitor.startScanMonitoring(scanId);

    logger.debug(`🔗 Integrated monitoring started for scan: ${scanId}`);
  }

  /**
   * Record utility performance for a check
   */
  recordUtilityPerformance(
    scanId: string,
    ruleId: string,
    startTime: number,
    endTime: number,
    utilitiesUsed: string[],
    cacheHits: number,
    cacheMisses: number,
    integrationStrategy: 'supplement' | 'enhance' | 'validate',
    errors: string[] = [],
  ): void {
    const totalTime = endTime - startTime;
    const cacheHitRate =
      cacheHits + cacheMisses > 0 ? (cacheHits / (cacheHits + cacheMisses)) * 100 : 0;

    // Get utility-specific execution times from UtilityIntegrationManager
    const utilityMetrics = this.utilityManager.getPerformanceMetrics(ruleId);
    const utilityExecutionTimes: Record<string, number> = {};

    if (utilityMetrics) {
      // Calculate individual utility times (simplified)
      const avgTimePerUtility = utilityMetrics.averageExecutionTime / utilitiesUsed.length;
      utilitiesUsed.forEach((utility) => {
        utilityExecutionTimes[utility] = avgTimePerUtility;
      });
    }

    const totalUtilityTime = Object.values(utilityExecutionTimes).reduce(
      (sum, time) => sum + time,
      0,
    );
    const utilityOverhead = totalTime > 0 ? (totalUtilityTime / totalTime) * 100 : 0;

    const utilityPerformanceMetrics: UtilityPerformanceMetrics = {
      utilitiesUsed,
      utilityExecutionTimes,
      cacheHits,
      cacheMisses,
      cacheHitRate,
      integrationStrategy,
      totalUtilityTime,
      utilityOverhead,
      utilityErrors: errors,
    };

    // Record in performance monitor
    this.performanceMonitor.recordUtilityMetrics(scanId, ruleId, utilityPerformanceMetrics);

    // Check for performance alerts
    this.checkPerformanceAlerts(scanId, ruleId, utilityPerformanceMetrics);

    logger.debug(`🔧 Utility performance recorded for ${ruleId}:`, {
      utilitiesUsed: utilitiesUsed.length,
      cacheHitRate: cacheHitRate.toFixed(1) + '%',
      utilityOverhead: utilityOverhead.toFixed(1) + '%',
    });
  }

  /**
   * Check for performance alerts
   */
  private checkPerformanceAlerts(
    scanId: string,
    ruleId: string,
    metrics: UtilityPerformanceMetrics,
  ): void {
    const alerts: string[] = [];

    if (metrics.utilityOverhead > this.config.performanceThresholds.maxUtilityOverhead) {
      alerts.push(`High utility overhead (${metrics.utilityOverhead.toFixed(1)}%) for ${ruleId}`);
    }

    if (metrics.cacheHitRate < this.config.performanceThresholds.minCacheHitRate) {
      alerts.push(`Low cache hit rate (${metrics.cacheHitRate.toFixed(1)}%) for ${ruleId}`);
    }

    if (metrics.totalUtilityTime > this.config.performanceThresholds.maxUtilityExecutionTime) {
      alerts.push(`High utility execution time (${metrics.totalUtilityTime}ms) for ${ruleId}`);
    }

    if (metrics.utilityErrors.length > 0) {
      alerts.push(`Utility errors detected for ${ruleId}: ${metrics.utilityErrors.join(', ')}`);
    }

    if (alerts.length > 0) {
      logger.warn(`⚠️ Performance alerts for scan ${scanId}:`, alerts);

      // Send alerts to enhanced monitor
      alerts.forEach((alert) => {
        this.enhancedMonitor.recordAlert(scanId, 'performance', 'medium', alert);
      });
    }
  }

  /**
   * Collect real-time metrics
   */
  private collectRealTimeMetrics(): void {
    // Get metrics from utility manager
    const utilityStats = this.utilityManager.getPerformanceMetrics();

    if (utilityStats && utilityStats.totalExecutions > 0) {
      logger.debug('📊 Real-time utility metrics:', {
        totalExecutions: utilityStats.totalExecutions,
        averageExecutionTime: utilityStats.averageExecutionTime.toFixed(2) + 'ms',
        cacheHitRate: (utilityStats.cacheHitRate * 100).toFixed(1) + '%',
        totalErrors: utilityStats.totalErrors,
      });
    }
  }

  /**
   * Generate integrated performance report
   */
  generateIntegratedReport(scanId: string): IntegratedPerformanceReport | null {
    // Get base performance report
    const baseReport = this.performanceMonitor.generateReport(scanId);
    if (!baseReport) return null;

    // Get utility integration stats
    const utilityStats = this.performanceMonitor.getUtilityIntegrationStats(scanId);
    if (!utilityStats) return null;

    // Get enhanced metrics
    const enhancedStats = this.enhancedMonitor.getEnhancedStats();

    const report: IntegratedPerformanceReport = {
      scanId,
      timestamp: new Date(),
      overallMetrics: {
        totalDuration: baseReport.metrics.totalDuration || 0,
        checksExecuted: baseReport.metrics.overallStats.totalChecksExecuted,
        successRate:
          (baseReport.metrics.overallStats.successfulChecks /
            baseReport.metrics.overallStats.totalChecksExecuted) *
          100,
        memoryPeakMB: baseReport.metrics.overallStats.memoryPeakMB,
      },
      utilityMetrics: utilityStats,
      performanceInsights: {
        recommendations: baseReport.recommendations,
        optimizationOpportunities: this.generateOptimizationOpportunities(utilityStats),
        alerts: enhancedStats.recentAlerts.map((alert) => alert.message),
      },
      trends: {
        performanceImprovement: baseReport.comparisonToBaseline?.durationImprovement || 0,
        cacheEfficiencyTrend: this.calculateCacheEfficiencyTrend(scanId),
        utilityOptimizationScore: this.calculateUtilityOptimizationScore(utilityStats),
      },
    };

    // Store in history
    if (!this.metricsHistory.has(scanId)) {
      this.metricsHistory.set(scanId, []);
    }
    this.metricsHistory.get(scanId)!.push(report);

    logger.info(`📊 Integrated performance report generated for scan: ${scanId}`, {
      duration: report.overallMetrics.totalDuration,
      successRate: report.overallMetrics.successRate.toFixed(1) + '%',
      cacheHitRate: report.utilityMetrics.averageCacheHitRate.toFixed(1) + '%',
      utilityOptimizationScore: report.trends.utilityOptimizationScore,
    });

    return report;
  }

  /**
   * Generate optimization opportunities
   */
  private generateOptimizationOpportunities(utilityStats: any): string[] {
    const opportunities: string[] = [];

    if (utilityStats.averageCacheHitRate < 80) {
      opportunities.push('Improve cache hit rate by optimizing cache TTL settings');
    }

    if (utilityStats.averageUtilityOverhead > 20) {
      opportunities.push('Reduce utility overhead by optimizing integration strategies');
    }

    if (utilityStats.utilityErrors.length > 0) {
      opportunities.push('Address utility errors to improve reliability');
    }

    // Analyze most used utilities for optimization
    const topUtilities = utilityStats.mostUsedUtilities.slice(0, 3);
    topUtilities.forEach(({ utility, count }) => {
      if (count > 10) {
        opportunities.push(
          `Optimize ${utility} utility for better performance (used ${count} times)`,
        );
      }
    });

    return opportunities;
  }

  /**
   * Calculate cache efficiency trend
   */
  private calculateCacheEfficiencyTrend(scanId: string): number {
    const history = this.metricsHistory.get(scanId) || [];
    if (history.length < 2) return 0;

    const recent = history.slice(-2);
    const oldRate = recent[0].utilityMetrics.averageCacheHitRate;
    const newRate = recent[1].utilityMetrics.averageCacheHitRate;

    return ((newRate - oldRate) / oldRate) * 100;
  }

  /**
   * Calculate utility optimization score
   */
  private calculateUtilityOptimizationScore(utilityStats: any): number {
    let score = 100;

    // Deduct points for poor cache performance
    if (utilityStats.averageCacheHitRate < 70) {
      score -= 20;
    } else if (utilityStats.averageCacheHitRate < 85) {
      score -= 10;
    }

    // Deduct points for high overhead
    if (utilityStats.averageUtilityOverhead > 25) {
      score -= 20;
    } else if (utilityStats.averageUtilityOverhead > 15) {
      score -= 10;
    }

    // Deduct points for errors
    score -= Math.min(utilityStats.utilityErrors.length * 5, 30);

    return Math.max(score, 0);
  }

  /**
   * Get performance configuration
   */
  getConfig(): PerformanceIntegrationConfig {
    return { ...this.config };
  }

  /**
   * Update performance configuration
   */
  updateConfig(newConfig: Partial<PerformanceIntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('🔧 Performance integration configuration updated');
  }
}
